"use client";

import { motion } from 'framer-motion';
import Button from './Button';
import { useArcade } from '../contexts/ArcadeContext';

const ArcadeEntryPoint = () => {
  const { isArcadeActive, enterArcade, exitArcade } = useArcade();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
      className="text-center py-8 border-t border-secondary/20 mt-8"
    >
      {!isArcadeActive ? (
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <span className="text-2xl">🎮</span>
            <h3 className="font-heading font-bold text-secondary text-xl">
              A Little Bonus
            </h3>
            <span className="text-2xl">🕹️</span>
          </div>
          
          <p className="text-secondary/80 text-lg max-w-md mx-auto mb-6">
            Care for a nostalgia break? Check out some classic games recreated with modern web tech!
          </p>
          
          <Button
            variant="filled"
            onClick={enterArcade}
            className="bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent text-white font-bold px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
          >
            🚀 Enter Arcade
          </Button>
          
          <div className="flex items-center justify-center mt-4 space-x-2 text-secondary/60">
            <span className="text-sm">Interactive project showcase</span>
            <span className="animate-bounce">↓</span>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <span className="text-2xl animate-pulse">🎮</span>
            <h3 className="font-heading font-bold text-secondary text-xl">
              Arcade Active
            </h3>
            <span className="text-2xl animate-pulse">🕹️</span>
          </div>
          
          <p className="text-secondary/80 text-lg max-w-md mx-auto mb-6">
            Enjoying the games? You can return to the main portfolio anytime.
          </p>
          
          <Button
            variant="outline"
            onClick={exitArcade}
            className="border-accent text-accent hover:bg-accent hover:text-white font-bold px-8 py-3 text-lg transition-all duration-300"
          >
            ← Back to Portfolio
          </Button>
        </div>
      )}
    </motion.div>
  );
};

export default ArcadeEntryPoint;
