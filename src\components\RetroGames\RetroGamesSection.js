"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';
import { useArcade } from '../../contexts/ArcadeContext';

const RetroGamesSection = () => {
  const { isArcadeActive, enterArcade } = useArcade();
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, Snake became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game where you arrange falling pieces to clear lines.',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩',
    //   trivia: 'Created by <PERSON><PERSON> in 1984, Tetris is one of the most recognizable puzzle games ever made. Coming soon to this arcade!'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the flying ducks before they escape off screen.',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆',
    //   trivia: 'Released by Nintendo in 1984, Duck Hunt used a light gun accessory. This web version uses mouse clicks instead!'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 300); // Faster transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen flex items-center justify-center relative z-10"
    >
      {/* Container with natural spacing */}
      <div className="w-3/4 mx-auto">
        <AnimatePresence mode="wait">
          {!isArcadeActive ? (
            // Entry Card State - Central card with enter button
            <motion.div
              key="entry-card"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="flex items-center justify-center"
              style={{ height: '70vh' }}
            >
              <div className="bg-primary rounded-3xl p-12 text-center max-w-2xl shadow-2xl border border-secondary/10">
              <div className="space-y-6">
                <div className="flex items-center justify-center space-x-3 mb-6">
                  <span className="text-3xl animate-pulse">🎮</span>
                  <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl">
                    Retro Arcade
                  </h2>
                  <span className="text-3xl animate-pulse">🕹️</span>
                </div>

                <p className="text-secondary text-lg leading-relaxed mb-6">
                  Care for a nostalgia break? Check out some classic games recreated with modern web tech for your enjoyment.
                </p>

                <p className="text-secondary/80 text-base mb-8">
                  Want to see a more interactive project? Step into the arcade!
                </p>

                <motion.button
                  onClick={enterArcade}
                  className="bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent text-white font-semibold px-6 py-2 text-base rounded-full shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Enter Arcade
                </motion.button>
              </div>
              </div>
            </motion.div>
          ) : (
            // Active Arcade State - Original layout
            <motion.div
              key="active-arcade"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              style={{ height: '70vh' }}
            >
          {/* Game Text - Left Side (1 column) */}
          <div className="lg:col-span-1 flex items-center bg-primary rounded-2xl p-8">
            <div className="w-full">
              <AnimatePresence mode="wait">
                {!selectedGame ? (
                  <motion.div
                    key="console-text"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                  >
                    <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl mb-8">
                      🎮 Retro Arcade
                    </h2>
                    <p className="text-secondary text-lg mb-8">
                      Take a nostalgia break! Classic games recreated with modern web tech for your enjoyment.
                    </p>
                    <div className="text-secondary/60 text-sm">
                      <p>Click on a game card to start playing →</p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="game-text"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                  >
                    <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-5xl mb-6">
                      {selectedGame.icon} {selectedGame.title}
                    </h2>
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-heading font-bold text-secondary text-lg mb-2">Game Info</h3>
                        <p className="text-secondary text-lg mb-4">{selectedGame.description}</p>
                      </div>
                      <div>
                        <h3 className="font-heading font-bold text-secondary text-lg mb-2">Controls</h3>
                        <p className="text-secondary/80 text-sm mb-4">
                          Use <span className="bg-secondary/20 px-2 py-1 rounded">WASD</span> or <span className="bg-secondary/20 px-2 py-1 rounded">Arrow Keys</span> to move
                        </p>
                      </div>
                      <div>
                        <h3 className="font-heading font-bold text-secondary text-lg mb-2">Trivia</h3>
                        <p className="text-secondary/80 text-sm">
                          {selectedGame.trivia || "Classic arcade game recreated with modern web technologies for your enjoyment!"}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Game Area - Right Side (2 columns) - Fixed height */}
          <div className="lg:col-span-2 flex items-center justify-center bg-background rounded-2xl p-8" style={{ height: '70vh' }}>
            <AnimatePresence mode="wait">
              {!selectedGame ? (
                <motion.div
                  key="console"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                  className="w-full flex items-center justify-center"
                >
                  <GameConsole
                    games={games}
                    onGameSelect={handleGameSelect}
                    isTransitioning={isTransitioning}
                  />
                </motion.div>
              ) : (
                <motion.div
                  key="game-screen"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                  className="w-full flex items-center justify-center"
                >
                  <GameScreen
                    game={selectedGame}
                    onBack={handleBackToConsole}
                    isTransitioning={isTransitioning}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default RetroGamesSection;
