"use client";

import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';

const ProjectDetails = ({ project }) => {
  const { theme } = useTheme();

  if (!project) {
    return (
      <div className="p-8 pr-16">
        <h2 className="text-2xl font-heading font-bold text-secondary mb-4">
          Project Details
        </h2>
        <p className="text-secondary/80">
          No project data available.
        </p>
      </div>
    );
  }

  // Tech stack icon mapping
  const getIconPath = (techName) => {
    const iconMap = {
      'PS': '/Creative Software Icons/Photoshop-512.png',
      'AI': '/Creative Software Icons/Illustrator-512.png',
      'VS Code': '/Creative Software Icons/vscode.svg',
      'GPT': theme === 'dark'
        ? '/Creative Software Icons/OpenAI-white-monoblossom.svg'
        : '/Creative Software Icons/OpenAI-black-monoblossom.svg'
    };
    return iconMap[techName] || null;
  };

  return (
    <div className="p-8 pr-16">
      {/* Project Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-heading font-bold text-secondary mb-4">
          {project.title}
        </h1>
        <p className="text-lg text-secondary/80 leading-relaxed">
          {project.description}
        </p>
      </div>

      {/* Project Image */}
      {project.image && (
        <div className="mb-8">
          <div className="shadow-lg">
            <img
              src={project.image}
              alt={project.title}
              className="w-full h-auto object-cover"
            />
          </div>
        </div>
      )}

      {/* Tech Stack */}
      {project.techStack && project.techStack.length > 0 && (
        <div className="mb-8">
          <h3 className="text-xl font-heading font-semibold text-secondary mb-4">
            Tech Stack
          </h3>
          <div className="flex flex-wrap gap-4">
            {project.techStack.map((tech) => {
              const iconPath = getIconPath(tech);

              return iconPath ? (
                <div key={tech} className="w-8 h-8 flex items-center justify-center">
                  <img
                    src={iconPath}
                    alt={tech}
                    className="w-full h-full object-contain"
                  />
                </div>
              ) : (
                <span
                  key={tech}
                  className="bg-primary border border-secondary/30 text-secondary px-4 py-2 rounded-lg font-medium"
                >
                  {tech}
                </span>
              );
            })}
          </div>
        </div>
      )}

      {/* Project Details Section */}
      <div className="space-y-6">
        <h3 className="text-xl font-heading font-semibold text-secondary">
          Project Details
        </h3>
        
        {/* Add more detailed content based on project type */}
        {project.id === 1 && (
          <div className="space-y-4">
            <p className="text-secondary/80 leading-relaxed">
              This collection showcases poster designs for various musical productions including classical ballets and contemporary musicals. Each design captures the essence and mood of the performance while maintaining strong visual impact for both print and digital applications.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-secondary mb-2">Key Features:</h4>
                <ul className="space-y-1 text-secondary/80">
                  <li>• Typography-focused design approach</li>
                  <li>• Print-ready high resolution outputs</li>
                  <li>• Social media optimized variants</li>
                  <li>• Brand consistency across series</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-secondary mb-2">Productions:</h4>
                <ul className="space-y-1 text-secondary/80">
                  <li>• Lacul Lebedelor (Swan Lake)</li>
                  <li>• Mamma Mia!</li>
                  <li>• The Nutcracker</li>
                  <li>• Various seasonal shows</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {project.id === 2 && (
          <div className="space-y-4">
            <p className="text-secondary/80 leading-relaxed">
              A curated collection of logo designs spanning various industries and brand personalities. Each logo is crafted with intentional symbolism and typography choices that reflect the brand's core values and target audience.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-secondary mb-2">Design Principles:</h4>
                <ul className="space-y-1 text-secondary/80">
                  <li>• Meaningful symbolism</li>
                  <li>• Scalable vector graphics</li>
                  <li>• Industry-appropriate aesthetics</li>
                  <li>• Timeless design approach</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-secondary mb-2">Applications:</h4>
                <ul className="space-y-1 text-secondary/80">
                  <li>• Business cards and stationery</li>
                  <li>• Digital brand guidelines</li>
                  <li>• Social media assets</li>
                  <li>• Website integration</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {project.id === 3 && (
          <div className="space-y-4">
            <p className="text-secondary/80 leading-relaxed">
              A showcase of landing page designs focused on conversion optimization and user experience. Each design balances visual appeal with functional clarity to guide users toward specific actions.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-secondary mb-2">Design Focus:</h4>
                <ul className="space-y-1 text-secondary/80">
                  <li>• Conversion-optimized layouts</li>
                  <li>• Clear visual hierarchy</li>
                  <li>• Mobile-first responsive design</li>
                  <li>• Fast loading optimization</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-secondary mb-2">Technologies:</h4>
                <ul className="space-y-1 text-secondary/80">
                  <li>• Modern CSS frameworks</li>
                  <li>• JavaScript interactions</li>
                  <li>• AI-assisted content creation</li>
                  <li>• Performance optimization</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectDetails;
