"use client";

import { motion } from 'framer-motion';

const GameConsole = ({ games, onGameSelect, isTransitioning }) => {
  return (
    <motion.div
      className="w-full max-w-3xl"
      initial={{ opacity: 1, scale: 1 }}
      animate={{
        opacity: isTransitioning ? 0 : 1,
        scale: isTransitioning ? 0.9 : 1
      }}
      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
    >
      {/* Game Cards Grid - Optimized for 2/3 layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {games.map((game) => (
            <motion.div
              key={game.id}
              className="bg-background rounded-2xl cursor-pointer group hover:bg-background/80 transition-all duration-150 shadow-lg hover:shadow-xl border border-secondary/20"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.15, ease: [0, 0, 0.2, 1] }}
              onClick={() => onGameSelect(game)}
              style={{
                transformOrigin: 'center center',
                transform: 'translateZ(0)' // Force hardware acceleration for smoother scaling
              }}
            >
              {/* Game Content */}
              <div className="text-center p-6">
                <div
                  className="w-16 h-16 mx-auto rounded-2xl flex items-center justify-center text-3xl mb-3"
                  style={{ backgroundColor: game.color + '20' }}
                >
                  {game.icon}
                </div>
                <h4 className="font-heading font-bold text-secondary text-lg">
                  {game.title}
                </h4>
              </div>
            </motion.div>
          ))}

          {/* Coming Soon Cards */}
          {[...Array(2)].map((_, index) => (
            <motion.div
              key={`coming-soon-${index}`}
              className="bg-background/30 rounded-2xl opacity-40 border border-secondary/10"
            >
              <div className="text-center p-6">
                <div className="w-16 h-16 mx-auto rounded-2xl bg-secondary/10 flex items-center justify-center text-3xl mb-3">
                  🎮
                </div>
                <h4 className="font-heading font-bold text-secondary/50 text-lg">
                  Coming Soon
                </h4>
              </div>
            </motion.div>
          ))}
        </div>
    </motion.div>
  );
};

export default GameConsole;
