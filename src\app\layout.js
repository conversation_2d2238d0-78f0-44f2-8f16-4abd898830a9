import { Open_Sans, Poppins } from "next/font/google";
import "./globals.css";
import ClientThemeProvider from "../components/ClientThemeProvider";
import { ArcadeProvider } from "../contexts/ArcadeContext";

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  display: "swap",
  weight: ["400", "700"], // Example weights, adjust as needed
});

export const metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${openSans.variable} ${poppins.variable} antialiased`}
      >
        <ClientThemeProvider>
          <ArcadeProvider>
            {children}
          </ArcadeProvider>
        </ClientThemeProvider>
      </body>
    </html>
  );
}
